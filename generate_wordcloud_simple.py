#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from wordcloud import WordCloud
import matplotlib.pyplot as plt
import os
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

def read_excel_data(file_path):
    """读取Excel文件"""
    try:
        df = pd.read_excel(file_path)
        print(f"成功读取文件: {file_path}")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

def generate_wordcloud_for_topic(df, topic_name, output_dir='wordclouds'):
    """为特定话题生成词云图"""
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 筛选特定话题的数据
    topic_data = df[df['topic'].astype(str).str.upper().str.contains(topic_name.upper(), na=False)]
    
    if topic_data.empty:
        print(f"未找到话题 {topic_name} 的数据")
        return
    
    print(f"\n话题 {topic_name} 共有 {len(topic_data)} 条数据")
    
    # 收集所有标签文本（完整标签，不分词）
    labels = topic_data['simplified_label'].dropna().astype(str)
    
    if labels.empty:
        print(f"话题 {topic_name} 没有有效的标签数据")
        return
    
    # 统计完整标签的频次
    label_freq = Counter(labels)
    
    print(f"话题 {topic_name} 最常见的10个标签:")
    for label, freq in label_freq.most_common(10):
        print(f"  {label}: {freq}")
    
    # 生成词云（使用完整标签）
    wordcloud = WordCloud(
        width=1200, 
        height=800,
        background_color='white',
        max_words=50,  # 减少词数以适应完整标签
        relative_scaling=0.3,
        colormap='viridis',
        collocations=False,  # 避免词汇组合
        prefer_horizontal=0.7,  # 70%的词汇水平显示
        font_step=1,
        max_font_size=100,
        min_font_size=10
    ).generate_from_frequencies(label_freq)
    
    # 保存词云图
    plt.figure(figsize=(12, 8))
    plt.imshow(wordcloud, interpolation='bilinear')
    plt.axis('off')
    plt.title(f'{topic_name} Topic Label Word Cloud', fontsize=16, pad=20)
    
    output_path = os.path.join(output_dir, f'{topic_name}_wordcloud.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()  # 关闭图形以释放内存
    
    print(f"词云图已保存到: {output_path}")
    
    # 同时生成一个文本统计文件
    stats_path = os.path.join(output_dir, f'{topic_name}_label_stats.txt')
    with open(stats_path, 'w', encoding='utf-8') as f:
        f.write(f"{topic_name} 话题标签统计\n")
        f.write("=" * 50 + "\n")
        f.write(f"总数据量: {len(topic_data)}\n")
        f.write(f"有效标签数: {len(labels)}\n")
        f.write(f"唯一标签数: {len(label_freq)}\n\n")
        f.write("标签频次统计:\n")
        f.write("-" * 30 + "\n")
        for i, (label, freq) in enumerate(label_freq.most_common(), 1):
            f.write(f"{i:2d}. {label}: {freq}\n")
    
    print(f"统计文件已保存到: {stats_path}")

def main():
    # 文件路径
    file_path = 'simplified_motivations_mapping_for_user.xlsx'
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        print("当前目录文件:", os.listdir('.'))
        return
    
    # 读取数据
    df = read_excel_data(file_path)
    if df is None:
        return
    
    print("\n=== 数据概览 ===")
    print("前5行数据:")
    print(df[['topic', 'simplified_label']].head())
    
    print(f"\n话题分布:")
    topic_counts = df['topic'].value_counts()
    for topic, count in topic_counts.items():
        print(f"  {topic}: {count} 条")
    
    # 为三种话题生成词云图
    topics = ['CUT', 'FRIR', 'DEI']
    
    for topic in topics:
        try:
            generate_wordcloud_for_topic(df, topic)
        except Exception as e:
            print(f"生成 {topic} 词云图时出错: {e}")
    
    print("\n=== 完成 ===")
    print("所有词云图和统计文件已生成完毕！")

if __name__ == "__main__":
    main()
