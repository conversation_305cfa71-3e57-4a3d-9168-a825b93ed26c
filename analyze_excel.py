#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计Excel文件中CUT、FRIR和DEi三种话题下E列的意图数量
"""

import pandas as pd
import sys
from collections import Counter

def analyze_excel_file(file_path):
    """
    分析Excel文件，统计CUT、FRIR和DEi三种话题下E列的意图数量
    
    Args:
        file_path (str): Excel文件路径
    
    Returns:
        dict: 包含统计结果的字典
    """
    try:
        # 读取Excel文件
        print(f"正在读取文件: {file_path}")
        df = pd.read_excel(file_path)
        
        # 显示文件基本信息
        print(f"文件包含 {len(df)} 行数据")
        print(f"列名: {list(df.columns)}")
        print("\n前5行数据预览:")
        print(df.head())
        
        # 检查是否有E列
        if 'E' not in df.columns and len(df.columns) >= 5:
            # 如果没有E列名，使用第5列（索引4）
            e_column = df.columns[4]
            print(f"\n使用第5列作为E列: {e_column}")
        elif 'E' in df.columns:
            e_column = 'E'
            print(f"\n找到E列: {e_column}")
        else:
            print("\n错误: 找不到E列或第5列")
            return None
        
        # 查找话题列（通常是第一列或包含话题信息的列）
        topic_column = None
        for col in df.columns:
            # 检查列中是否包含CUT、FRIR、DEi等话题
            if df[col].astype(str).str.contains('CUT|FRIR|DEi', case=False, na=False).any():
                topic_column = col
                break
        
        if topic_column is None:
            print("\n在所有列中搜索话题信息...")
            # 显示所有列的唯一值，帮助识别话题列
            for i, col in enumerate(df.columns):
                unique_values = df[col].dropna().unique()
                print(f"\n列 {i+1} ({col}) 的唯一值 (前10个):")
                print(unique_values[:10])
                
                # 检查是否包含目标话题
                contains_topics = any(str(val).upper() in ['CUT', 'FRIR', 'DEI'] for val in unique_values)
                if contains_topics:
                    topic_column = col
                    print(f"*** 在列 {col} 中找到话题信息 ***")
                    break
        
        if topic_column is None:
            print("\n警告: 未找到包含CUT、FRIR、DEi话题的列")
            print("将尝试在所有行中搜索这些话题...")
            
            # 在整个数据框中搜索话题
            results = {}
            for topic in ['CUT', 'FRIR', 'DEi']:
                print(f"\n搜索话题: {topic}")
                # 在所有列中搜索该话题
                mask = df.astype(str).apply(lambda x: x.str.contains(topic, case=False, na=False)).any(axis=1)
                topic_rows = df[mask]
                
                if len(topic_rows) > 0:
                    print(f"找到 {len(topic_rows)} 行包含 {topic}")
                    # 获取这些行的E列值
                    e_values = topic_rows[e_column].dropna().astype(str)
                    unique_intentions = e_values[e_values != ''].unique()
                    results[topic] = {
                        'count': len(unique_intentions),
                        'intentions': list(unique_intentions)
                    }
                    print(f"{topic} 话题下E列有 {len(unique_intentions)} 种不同的意图")
                else:
                    print(f"未找到包含 {topic} 的行")
                    results[topic] = {'count': 0, 'intentions': []}
            
            return results
        
        print(f"\n使用话题列: {topic_column}")
        print(f"使用意图列: {e_column}")
        
        # 统计每个话题下E列的意图数量
        results = {}
        
        for topic in ['CUT', 'FRIR', 'DEi']:
            print(f"\n分析话题: {topic}")
            
            # 筛选包含该话题的行
            topic_mask = df[topic_column].astype(str).str.contains(topic, case=False, na=False)
            topic_data = df[topic_mask]
            
            if len(topic_data) == 0:
                print(f"未找到话题 {topic} 的数据")
                results[topic] = {'count': 0, 'intentions': []}
                continue
            
            print(f"找到 {len(topic_data)} 行 {topic} 话题的数据")
            
            # 获取E列的值
            e_values = topic_data[e_column].dropna().astype(str)
            
            # 过滤空值和无效值
            valid_intentions = e_values[e_values.str.strip() != ''].unique()
            
            results[topic] = {
                'count': len(valid_intentions),
                'intentions': list(valid_intentions)
            }
            
            print(f"{topic} 话题下E列有 {len(valid_intentions)} 种不同的意图")
            if len(valid_intentions) <= 10:  # 如果意图数量不多，显示所有
                print(f"意图列表: {list(valid_intentions)}")
            else:  # 如果太多，只显示前10个
                print(f"前10个意图: {list(valid_intentions[:10])}")
        
        return results
        
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def main():
    file_path = "simplified_motivations_mapping_for_user.xlsx"
    
    print("=" * 60)
    print("Excel文件意图统计分析")
    print("=" * 60)
    
    results = analyze_excel_file(file_path)
    
    if results:
        print("\n" + "=" * 60)
        print("统计结果汇总")
        print("=" * 60)
        
        total_intentions = 0
        for topic, data in results.items():
            count = data['count']
            total_intentions += count
            print(f"{topic:6} 话题: {count:3d} 种意图")
        
        print(f"{'总计':6}: {total_intentions:3d} 种意图")
        
        # 详细结果
        print("\n" + "=" * 60)
        print("详细意图列表")
        print("=" * 60)
        
        for topic, data in results.items():
            print(f"\n{topic} 话题 ({data['count']} 种意图):")
            for i, intention in enumerate(data['intentions'], 1):
                print(f"  {i:2d}. {intention}")
    else:
        print("分析失败，请检查文件格式和内容")

if __name__ == "__main__":
    main()
