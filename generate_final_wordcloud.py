#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from wordcloud import WordCloud
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial Unicode MS', 'SimHei', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def find_chinese_font():
    """查找系统中可用的中文字体"""
    # 常见的中文字体名称
    chinese_font_names = [
        'SimHei', 'SimSun', 'Microsoft YaHei', 'WenQuanYi Micro Hei',
        'Noto Sans CJK SC', 'Source Han Sans SC', 'Droid Sans Fallback',
        'AR PL UMing CN', 'AR PL UKai CN'
    ]
    
    # 获取系统所有字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 查找可用的中文字体
    for font_name in chinese_font_names:
        if font_name in available_fonts:
            print(f"找到中文字体: {font_name}")
            return font_name
    
    # 如果没有找到专门的中文字体，查找包含CJK的字体
    for font in fm.fontManager.ttflist:
        if any(keyword in font.name.lower() for keyword in ['cjk', 'han', 'noto', 'droid']):
            print(f"找到CJK字体: {font.name}")
            return font.name
    
    print("未找到中文字体，使用默认字体")
    return None

def generate_wordcloud_for_topic(df, topic_name, output_dir='final_wordclouds'):
    """为特定话题生成词云图"""
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 筛选特定话题的数据
    topic_data = df[df['topic'].astype(str).str.upper().str.contains(topic_name.upper(), na=False)]
    
    if topic_data.empty:
        print(f"未找到话题 {topic_name} 的数据")
        return
    
    print(f"\n=== 处理话题 {topic_name} ===")
    print(f"数据量: {len(topic_data)} 条")
    
    # 收集所有标签文本（完整标签，不分词）
    labels = topic_data['simplified_label'].dropna().astype(str)
    
    if labels.empty:
        print(f"话题 {topic_name} 没有有效的标签数据")
        return
    
    # 统计完整标签的频次
    label_freq = Counter(labels)
    
    print(f"有效标签数: {len(labels)}")
    print(f"唯一标签数: {len(label_freq)}")
    print(f"最常见的5个标签:")
    for label, freq in label_freq.most_common(5):
        print(f"  {label}: {freq}次")
    
    # 查找中文字体
    chinese_font = find_chinese_font()
    
    # 生成词云参数
    wordcloud_params = {
        'width': 1600,
        'height': 1000,
        'background_color': 'white',
        'max_words': 100,
        'relative_scaling': 0.4,
        'colormap': 'Set3',  # 使用更柔和的颜色
        'collocations': False,
        'prefer_horizontal': 0.8,
        'font_step': 1,
        'max_font_size': 80,
        'min_font_size': 12,
        'margin': 10
    }
    
    # 如果找到中文字体，添加字体路径
    if chinese_font:
        # 尝试获取字体文件路径
        for font in fm.fontManager.ttflist:
            if font.name == chinese_font:
                wordcloud_params['font_path'] = font.fname
                break
    
    # 生成词云
    try:
        wordcloud = WordCloud(**wordcloud_params).generate_from_frequencies(label_freq)
        
        # 创建图形
        fig, ax = plt.subplots(figsize=(16, 10))
        ax.imshow(wordcloud, interpolation='bilinear')
        ax.axis('off')
        
        # 设置标题
        title = f'{topic_name} Topic - Label Word Cloud'
        ax.set_title(title, fontsize=20, pad=20, weight='bold')
        
        # 保存高质量图片
        output_path = os.path.join(output_dir, f'{topic_name}_wordcloud_final.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none', format='png')
        plt.close()
        
        print(f"✓ 词云图已保存: {output_path}")
        
        # 生成详细统计报告
        generate_stats_report(topic_name, topic_data, label_freq, output_dir)
        
    except Exception as e:
        print(f"✗ 生成词云图失败: {e}")

def generate_stats_report(topic_name, topic_data, label_freq, output_dir):
    """生成详细的统计报告"""
    
    stats_path = os.path.join(output_dir, f'{topic_name}_detailed_stats.txt')
    
    with open(stats_path, 'w', encoding='utf-8') as f:
        f.write(f"{topic_name} 话题详细统计报告\n")
        f.write("=" * 60 + "\n\n")
        
        # 基本统计
        f.write("基本统计信息:\n")
        f.write("-" * 30 + "\n")
        f.write(f"总数据量: {len(topic_data)} 条\n")
        f.write(f"有效标签数: {len([l for l in topic_data['simplified_label'] if pd.notna(l)])}\n")
        f.write(f"唯一标签数: {len(label_freq)}\n")
        f.write(f"平均每个标签出现次数: {sum(label_freq.values()) / len(label_freq):.2f}\n\n")
        
        # 频次分布
        f.write("标签频次分布:\n")
        f.write("-" * 30 + "\n")
        freq_dist = Counter(label_freq.values())
        for freq, count in sorted(freq_dist.items(), reverse=True):
            f.write(f"出现{freq}次的标签: {count}个\n")
        f.write("\n")
        
        # 完整标签列表
        f.write("完整标签列表 (按频次排序):\n")
        f.write("-" * 40 + "\n")
        for i, (label, freq) in enumerate(label_freq.most_common(), 1):
            f.write(f"{i:3d}. {label} ({freq}次)\n")
    
    print(f"✓ 统计报告已保存: {stats_path}")

def main():
    print("=" * 60)
    print("中文标签词云图生成器")
    print("=" * 60)
    
    # 文件路径
    file_path = 'simplified_motivations_mapping_for_user.xlsx'
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"✗ 文件不存在: {file_path}")
        print("当前目录文件:", os.listdir('.'))
        return
    
    # 读取数据
    print(f"正在读取文件: {file_path}")
    try:
        df = pd.read_excel(file_path)
        print(f"✓ 文件读取成功")
        print(f"  数据形状: {df.shape}")
        print(f"  列名: {list(df.columns)}")
    except Exception as e:
        print(f"✗ 文件读取失败: {e}")
        return
    
    # 数据概览
    print(f"\n话题分布:")
    topic_counts = df['topic'].value_counts()
    for topic, count in topic_counts.items():
        print(f"  {topic}: {count} 条")
    
    # 为三种话题生成词云图
    topics = ['CUT', 'FRIR', 'DEI']
    
    print(f"\n开始生成词云图...")
    success_count = 0
    
    for topic in topics:
        try:
            generate_wordcloud_for_topic(df, topic)
            success_count += 1
        except Exception as e:
            print(f"✗ 处理话题 {topic} 时出错: {e}")
    
    print(f"\n" + "=" * 60)
    print(f"任务完成! 成功生成 {success_count}/{len(topics)} 个词云图")
    print(f"输出目录: final_wordclouds/")
    print("=" * 60)

if __name__ == "__main__":
    main()
