#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from wordcloud import WordCloud
import matplotlib.pyplot as plt
import jieba
import os
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

def read_excel_data(file_path):
    """读取Excel文件"""
    try:
        df = pd.read_excel(file_path)
        print(f"成功读取文件: {file_path}")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

def analyze_data_structure(df):
    """分析数据结构"""
    print("\n=== 数据结构分析 ===")
    print("前5行数据:")
    print(df.head())
    
    print("\n各列的唯一值数量:")
    for col in df.columns:
        unique_count = df[col].nunique()
        print(f"{col}: {unique_count} 个唯一值")
        
        # 如果唯一值较少，显示所有唯一值
        if unique_count <= 10:
            unique_vals = df[col].dropna().unique()
            print(f"  唯一值: {list(unique_vals)}")
    
    return df

def identify_topic_and_label_columns(df):
    """识别话题列和标签列"""
    print("\n=== 识别相关列 ===")

    # 寻找可能包含CUT、FRIR、DEI的列
    topic_col = None
    label_col = None

    # 检查每一列是否包含这些话题
    for col in df.columns:
        col_values = df[col].astype(str).str.upper()
        if any(topic in col_values.values for topic in ['CUT', 'FRIR', 'DEI']):
            topic_col = col
            print(f"发现话题列: {col}")
            break

    # 直接使用simplified_label列作为标签列
    if 'simplified_label' in df.columns:
        label_col = 'simplified_label'
        print(f"使用标签列: {label_col}")
    else:
        # 如果没有simplified_label，寻找其他标签列
        for col in df.columns:
            if 'LABEL' in col.upper() or '标签' in col:
                label_col = col
                print(f"发现标签列: {col}")
                break

    # 如果没有找到明确的列，让用户选择
    if topic_col is None:
        print("未找到明确的话题列，请查看数据结构选择合适的列")
        print("可用列:", list(df.columns))

    if label_col is None:
        print("未找到明确的标签列，请查看数据结构选择合适的列")
        print("可用列:", list(df.columns))

    return topic_col, label_col

def generate_wordcloud_for_topic(df, topic_col, label_col, topic_name, output_dir='wordclouds'):
    """为特定话题生成词云图"""

    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 筛选特定话题的数据
    topic_data = df[df[topic_col].astype(str).str.upper().str.contains(topic_name.upper(), na=False)]

    if topic_data.empty:
        print(f"未找到话题 {topic_name} 的数据")
        return

    print(f"\n话题 {topic_name} 共有 {len(topic_data)} 条数据")

    # 收集所有标签文本（完整标签，不分词）
    labels = topic_data[label_col].dropna().astype(str)

    if labels.empty:
        print(f"话题 {topic_name} 没有有效的标签数据")
        return

    # 统计完整标签的频次
    label_freq = Counter(labels)

    print(f"话题 {topic_name} 最常见的10个标签:")
    for label, freq in label_freq.most_common(10):
        print(f"  {label}: {freq}")

    # 下载并设置中文字体
    font_path = setup_chinese_font()

    # 生成词云（使用完整标签）
    wordcloud_params = {
        'width': 1200,
        'height': 800,
        'background_color': 'white',
        'max_words': 50,  # 减少词数以适应完整标签
        'relative_scaling': 0.3,
        'colormap': 'viridis',
        'collocations': False,  # 避免词汇组合
        'prefer_horizontal': 0.7  # 70%的词汇水平显示
    }

    # 只有在找到字体时才添加字体路径
    if font_path:
        wordcloud_params['font_path'] = font_path

    wordcloud = WordCloud(**wordcloud_params).generate_from_frequencies(label_freq)

    # 设置matplotlib的中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False

    # 保存词云图
    plt.figure(figsize=(12, 8))
    plt.imshow(wordcloud, interpolation='bilinear')
    plt.axis('off')
    plt.title(f'{topic_name} 话题标签词云图', fontsize=16, pad=20)

    output_path = os.path.join(output_dir, f'{topic_name}_wordcloud.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()  # 关闭图形以释放内存

    print(f"词云图已保存到: {output_path}")

def setup_chinese_font():
    """设置中文字体"""
    import matplotlib.font_manager as fm

    # 尝试查找系统中的中文字体
    chinese_fonts = []
    for font in fm.fontManager.ttflist:
        font_name = font.name.lower()
        if any(keyword in font_name for keyword in ['simhei', 'simsun', 'microsoft', 'noto', 'source han', 'droid']):
            chinese_fonts.append(font.fname)

    if chinese_fonts:
        print(f"找到中文字体: {chinese_fonts[0]}")
        return chinese_fonts[0]

    # 如果没有找到中文字体，尝试常见的系统字体路径
    possible_fonts = [
        '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
        '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',
        '/System/Library/Fonts/Arial.ttf',
        '/Windows/Fonts/simhei.ttf',
        '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc'
    ]

    for font_path in possible_fonts:
        if os.path.exists(font_path):
            print(f"使用系统字体: {font_path}")
            return font_path

    print("未找到合适的中文字体，将使用默认字体")
    return None

def main():
    # 文件路径
    file_path = 'simplified_motivations_mapping_for_user.xlsx'
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        print("当前目录文件:", os.listdir('.'))
        return
    
    # 读取数据
    df = read_excel_data(file_path)
    if df is None:
        return
    
    # 分析数据结构
    df = analyze_data_structure(df)
    
    # 识别话题列和标签列
    topic_col, label_col = identify_topic_and_label_columns(df)
    
    if topic_col is None or label_col is None:
        print("请手动指定话题列和标签列")
        return
    
    # 为三种话题生成词云图
    topics = ['CUT', 'FRIR', 'DEI']
    
    for topic in topics:
        try:
            generate_wordcloud_for_topic(df, topic_col, label_col, topic)
        except Exception as e:
            print(f"生成 {topic} 词云图时出错: {e}")

if __name__ == "__main__":
    main()
